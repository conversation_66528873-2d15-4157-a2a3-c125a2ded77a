import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
try:
    from statsmodels.tsa.stattools import acf, pacf
    from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False
    print("⚠️ statsmodels库未安装，ACF/PACF图功能将被禁用")
class TimeSeriesPlotter:
    """时间序列绘图工具类，用于绘制完整时序图和测试集对比图"""

    def __init__(self):
        """
        初始化绘图工具
        """
        
        # 设置固定配置 - 优化为更长更矮的图形
        self.figsize = (50, 6)  # 增加宽度，减少高度，便于查看细节
        self.line_width = 1.2   # 稍微减细线条，避免过于密集

        # 优化颜色对比度，便于区分
        self.train_real_color = '#1f77b4'      # 深蓝色 - 训练集真实值
        self.train_pred_color = '#ff7f0e'      # 橙色 - 训练集预测值
        self.test_real_color = '#2ca02c'       # 绿色 - 测试集真实值
        self.test_pred_color = '#d62728'       # 红色 - 测试集预测值
        self.split_color = '#7f7f7f'           # 灰色 - 分割线

        self.grid_alpha = 0.2   # 降低网格透明度
        self.font_size = 11
        self.title_font_size = 14
    
    # -------------- 绘制完整时序图（训练集+测试集） -------------- #
    def plot_full_time_series(self, dates_train, y_train, train_pred, 
                             dates_test, y_test, test_pred, 
                             model_name, output_path):
        """
        绘制完整时序图（训练集+测试集）
        
        :param dates_train: 训练集日期
        :param y_train: 训练集真实值
        :param train_pred: 训练集预测值
        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :param output_path: 图片保存路径
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建画布
        plt.figure(figsize=self.figsize)

        # 绘制训练集真实值 - 使用深蓝色实线
        plt.plot(dates_train, y_train, label="训练集真实值",
                 color=self.train_real_color, linewidth=self.line_width, alpha=0.8)

        # 绘制训练集预测值 - 使用橙色虚线
        plt.plot(dates_train, train_pred, label="训练集预测值",
                 color=self.train_pred_color, linestyle="--", linewidth=self.line_width, alpha=0.9)

        # 绘制测试集真实值 - 使用绿色实线，加粗突出
        plt.plot(dates_test, y_test, label="测试集真实值",
                 color=self.test_real_color, linewidth=self.line_width, alpha=0.9)

        # 绘制测试集预测值 - 使用红色虚线，加粗突出
        plt.plot(dates_test, test_pred, label="测试集预测值",
                 color=self.test_pred_color, linestyle="--", linewidth=self.line_width, alpha=0.9)

        # 添加训练集与测试集分隔线
        if dates_train and dates_test:
            split_date = dates_train[-1] if len(dates_train) > 0 else dates_test[0]
            plt.axvline(x=split_date, color=self.split_color,
                        linestyle=':', linewidth=2, label="训练集/测试集分割线")

        # 设置图表属性
        plt.title(f"{model_name} 模型完整时序预测对比（训练集+测试集）",
                 fontsize=self.title_font_size, fontweight='bold')
        plt.xlabel("时间", fontsize=self.font_size)
        plt.ylabel("负荷值", fontsize=self.font_size)
        plt.legend(fontsize=self.font_size)
        plt.grid(True, alpha=self.grid_alpha)
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存完整时序图
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"完整时序对比图已保存至: {output_path}")
    
    # -------------- 绘制测试集时序图 -------------- #
    def plot_test_only_comparison(self, dates_test, y_test, test_pred, model_name, output_path):
        """
        绘制单独的测试集对比图

        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :param output_path: 图片保存路径
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建画布 - 使用更长更矮的尺寸
        plt.figure(figsize=(24, 6))

        # 绘制测试集真实值 - 使用绿色实线，加粗突出
        plt.plot(dates_test, y_test, label="测试集真实值",
                 color=self.test_real_color, linewidth=2.5, alpha=0.9)

        # 绘制测试集预测值 - 使用红色虚线，加粗突出
        plt.plot(dates_test, test_pred, label="测试集预测值",
                 color=self.test_pred_color, linewidth=2.5, linestyle="--", alpha=0.9)

        # 设置图表属性
        plt.title(f"{model_name} 模型测试集预测对比", fontsize=16, fontweight='bold')
        plt.xlabel("时间", fontsize=12)
        plt.ylabel("负荷值", fontsize=12)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存测试集对比图
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"测试集对比图已保存至: {output_path}")

    def plot_all_charts_and_calculate_metrics(self, dates_train, y_train, train_pred,
                                            dates_test, y_test, test_pred, model_name):
        """
        一次性生成所有图表并计算评估指标

        :param dates_train: 训练集日期
        :param y_train: 训练集真实值
        :param train_pred: 训练集预测值
        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :return: 评估指标字典
        """
        # 1. 完整时序图
        full_plot_path = f"{model_name}_full_time_series.png"
        self.plot_full_time_series(
            dates_train, y_train, train_pred,
            dates_test, y_test, test_pred,
            model_name, full_plot_path
        )



        # 3. 测试集对比图
        test_plot_path = f"{model_name}_test_comparison.png"
        self.plot_test_only_comparison(
            dates_test, y_test, test_pred,
            model_name, test_plot_path
        )

        # 4. 计算并返回评估指标
        y_test_array = np.array(y_test).flatten()
        test_pred_array = np.array(test_pred).flatten()

        mae = np.mean(np.abs(y_test_array - test_pred_array))
        rmse = np.sqrt(np.mean((y_test_array - test_pred_array) ** 2))

        # 避免除零错误
        nonzero_mask = y_test_array != 0
        if np.sum(nonzero_mask) > 0:
            mape = np.mean(np.abs((y_test_array[nonzero_mask] - test_pred_array[nonzero_mask]) / y_test_array[nonzero_mask]) * 100)
        else:
            mape = float('inf')

        metrics = {
            'mae': mae,
            'rmse': rmse,
            'mape': mape
        }

        # 打印评估指标（与模型评估阶段使用相同的数据和计算方法）
        print(f"\n测试集详细评估指标（基于{len(y_test_array)}个样本）:")
        print(f"- MAE (平均绝对误差): {mae:.4f}")
        print(f"- RMSE (均方根误差): {rmse:.4f}")
        print(f"- MAPE (平均绝对百分比误差): {mape:.2f}%" if mape != float('inf') else "- MAPE: 无法计算（存在零值）")

        return metrics

    def analyze_optimal_lag(self, df_load, max_lag_limit=50):
        """
        分析ACF/PACF以确定最佳滞后期数，用于LSTM的sequence_length

        :param df_load: 负荷数据DataFrame，包含'dateTime'和'load'列
        :param max_lag_limit: 最大滞后期限制
        :return: 建议的序列长度
        """
        if not STATSMODELS_AVAILABLE:
            print("⚠️ statsmodels库未安装，无法进行ACF/PACF分析，使用默认序列长度24")
            return 24

        try:
            # 准备数据：去除缺失值
            load_data = df_load['load'].dropna()

            # 计算合适的滞后期数（不超过数据长度的1/4，且不超过max_lag_limit）
            max_lags = min(max_lag_limit, len(load_data) // 4)

            # 计算ACF和PACF
            acf_values = acf(load_data, nlags=max_lags, alpha=0.05)
            pacf_values = pacf(load_data, nlags=max_lags, alpha=0.05)

            # 提取ACF和PACF的值（不包括置信区间）
            if isinstance(acf_values, tuple):
                acf_vals = acf_values[0]  # 如果返回置信区间，取第一个元素
            else:
                acf_vals = acf_values

            if isinstance(pacf_values, tuple):
                pacf_vals = pacf_values[0]  # 如果返回置信区间，取第一个元素
            else:
                pacf_vals = pacf_values

            # 方法1: 找到PACF首次不显著的位置（绝对值小于阈值）
            # 使用95%置信区间的阈值：约1.96/sqrt(n)
            threshold = 1.96 / np.sqrt(len(load_data))

            optimal_lag_pacf = None
            for i in range(1, len(pacf_vals)):  # 从滞后1开始
                if abs(pacf_vals[i]) < threshold:
                    optimal_lag_pacf = i
                    break

            # 方法2: 找到ACF衰减到阈值以下的位置
            optimal_lag_acf = None
            for i in range(1, len(acf_vals)):  # 从滞后1开始
                if abs(acf_vals[i]) < threshold:
                    optimal_lag_acf = i
                    break

            # 方法3: 基于数据频率的经验法则
            # 如果是15分钟数据，一天有96个点，可能需要24-48个滞后期
            # 如果是小时数据，一天有24个点，可能需要12-24个滞后期
            data_points_per_day = self._estimate_data_frequency(df_load)
            empirical_lag = min(data_points_per_day, 48)  # 不超过48

            # 综合决策：取三种方法的中位数，但有合理的范围限制
            candidates = []
            if optimal_lag_pacf is not None and optimal_lag_pacf > 0:
                candidates.append(optimal_lag_pacf)
            if optimal_lag_acf is not None and optimal_lag_acf > 0:
                candidates.append(optimal_lag_acf)
            candidates.append(empirical_lag)

            if len(candidates) >= 2:  # 至少有两个有效建议
                # 取中位数
                suggested_lag = int(np.median(candidates))
                # 确保在合理范围内 (6-96)
                suggested_lag = max(6, min(suggested_lag, 96))

                print(f"🔍 ACF/PACF分析结果:")
                print(f"   - PACF建议滞后期: {optimal_lag_pacf}")
                print(f"   - ACF建议滞后期: {optimal_lag_acf}")
                print(f"   - 经验法则建议: {empirical_lag}")
                print(f"   - 最终建议序列长度: {suggested_lag}")

                return suggested_lag
            else:
                print(f"🔍 ACF/PACF分析结果:")
                print(f"   - PACF建议滞后期: {optimal_lag_pacf}")
                print(f"   - ACF建议滞后期: {optimal_lag_acf}")
                print(f"   - 经验法则建议: {empirical_lag}")
                print(f"   - ⚠️ 分析结果不够可靠，建议使用配置文件中的默认值")

                return None  # 返回None表示分析不可靠

        except Exception as e:
            print(f"⚠️ ACF/PACF分析失败: {e}，使用默认序列长度24")
            return 24

    def _estimate_data_frequency(self, df_load):
        """
        估算数据频率（每天的数据点数）

        :param df_load: 负荷数据DataFrame
        :return: 估算的每天数据点数
        """
        try:
            # 计算时间间隔
            time_diffs = pd.to_datetime(df_load['dateTime']).diff().dropna()
            median_interval = time_diffs.median()

            # 计算每天的数据点数
            points_per_day = pd.Timedelta(days=1) / median_interval
            return int(points_per_day)
        except:
            return 96  # 默认假设15分钟间隔

    def plot_raw_load_data(self, df_load, preprocessor=None, output_path="raw_load_data.png"):
        """
        绘制原始负荷数据时间序列图，包含ACF和PACF分析图

        :param df_load: 负荷数据DataFrame，包含'dateTime'和'load'列
        :param preprocessor: DataPreprocessor实例，用于获取节假日信息
        :param output_path: 图片保存路径
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 根据是否有statsmodels库决定布局 - 使用更长更矮的尺寸
        if STATSMODELS_AVAILABLE:
            # 创建包含3个子图的画布：时序图、ACF图、PACF图
            fig, axes = plt.subplots(3, 1, figsize=(24, 10))  # 更长更矮
            ax_main = axes[0]
        else:
            # 只创建时序图
            fig, ax_main = plt.subplots(1, 1, figsize=(24, 6))  # 更长更矮
            axes = [ax_main]

        # === 第一个子图：原始时序图 ===
        plt.sca(ax_main)  # 设置当前轴

        # 如果提供了preprocessor，添加周末和节假日背景
        if preprocessor is not None:
            self._add_weekend_holiday_background(df_load, preprocessor)

        # 绘制负荷数据
        ax_main.plot(df_load['dateTime'], df_load['load'],
                    color='steelblue', linewidth=1.5, alpha=0.9, zorder=3)

        # 设置图表属性
        title_suffix = "（含ACF/PACF分析）" if STATSMODELS_AVAILABLE else "（含周末节假日标识）"
        ax_main.set_title(f"原始负荷数据时间序列{title_suffix}", fontsize=16, fontweight='bold', pad=20)
        ax_main.set_xlabel("时间", fontsize=12)
        ax_main.set_ylabel("负荷值 (kW)", fontsize=12)
        ax_main.grid(True, alpha=0.3, linestyle='--', zorder=1)
        ax_main.tick_params(axis='x', rotation=45)

        # 添加统计信息
        load_mean = df_load['load'].mean()
        load_max = df_load['load'].max()
        load_min = df_load['load'].min()

        # 在图上添加统计信息文本
        stats_text = f"统计信息:\n平均值: {load_mean:.2f} kW\n最大值: {load_max:.2f} kW\n最小值: {load_min:.2f} kW\n数据点数: {len(df_load)}"
        ax_main.text(0.02, 0.98, stats_text, transform=ax_main.transAxes,
                    fontsize=10, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8), zorder=4)

        # 添加图例（如果有背景标识）
        if preprocessor is not None:
            from matplotlib.patches import Patch
            from matplotlib.lines import Line2D
            legend_elements = [
                Patch(facecolor='lightcoral', alpha=0.3, label='周末'),
                Patch(facecolor='lightblue', alpha=0.4, label='法定节假日'),
                Line2D([0], [0], color='steelblue', linewidth=1.5, label='负荷数据')
            ]
            ax_main.legend(handles=legend_elements, loc='upper right', fontsize=10)

        # === 绘制ACF和PACF图（如果statsmodels可用） ===
        if STATSMODELS_AVAILABLE and len(axes) == 3:
            try:
                # 准备数据：去除缺失值
                load_data = df_load['load'].dropna()

                # 计算合适的滞后期数（不超过数据长度的1/4，且不超过50）
                max_lags = min(50, len(load_data) // 4)

                # === 第二个子图：ACF图 ===
                ax_acf = axes[1]
                plot_acf(load_data, lags=max_lags, ax=ax_acf, alpha=0.05)
                ax_acf.set_title("自相关函数 (ACF)", fontsize=14, fontweight='bold')
                ax_acf.set_xlabel("滞后期", fontsize=12)
                ax_acf.set_ylabel("自相关系数", fontsize=12)
                ax_acf.grid(True, alpha=0.3)

                # === 第三个子图：PACF图 ===
                ax_pacf = axes[2]
                plot_pacf(load_data, lags=max_lags, ax=ax_pacf, alpha=0.05)
                ax_pacf.set_title("偏自相关函数 (PACF)", fontsize=14, fontweight='bold')
                ax_pacf.set_xlabel("滞后期", fontsize=12)
                ax_pacf.set_ylabel("偏自相关系数", fontsize=12)
                ax_pacf.grid(True, alpha=0.3)

                print(f"✅ ACF/PACF分析完成，使用滞后期数: {max_lags}")

            except Exception as e:
                print(f"⚠️ ACF/PACF图绘制失败: {e}")
                # 如果ACF/PACF绘制失败，隐藏这两个子图
                if len(axes) >= 3:
                    axes[1].set_visible(False)
                    axes[2].set_visible(False)

        # 调整布局
        plt.tight_layout()

        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        acf_pacf_info = " (含ACF/PACF分析)" if STATSMODELS_AVAILABLE else ""
        print(f"原始负荷数据图已保存至: {output_path}{acf_pacf_info}")

        return {
            'mean': load_mean,
            'max': load_max,
            'min': load_min,
            'count': len(df_load)
        }

    def _add_weekend_holiday_background(self, df_load, preprocessor):
        """
        添加周末和节假日背景区域

        :param df_load: 负荷数据DataFrame
        :param preprocessor: DataPreprocessor实例
        """
        # 获取时间范围
        start_date = df_load['dateTime'].min().strftime('%Y-%m-%d')
        end_date = df_load['dateTime'].max().strftime('%Y-%m-%d')

        # 创建临时DataFrame用于获取节假日信息
        temp_df = pd.DataFrame({'dateTime': df_load['dateTime']})
        temp_df['date'] = temp_df['dateTime'].dt.date.astype(str)

        # 使用chinese_calendar库添加日期类型特征（与data_loader.py保持一致）
        try:
            import chinese_calendar as cc
            from datetime import datetime

            def get_day_type(date_str):
                """
                获取日期类型：1=工作日, 2=周末, 3=法定节假日
                """
                date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                holiday_detail = cc.get_holiday_detail(date_obj)

                if holiday_detail[0] and holiday_detail[1] is not None:  # 有具体节假日名称
                    return 3  # 法定节假日（如五一、端午等）
                elif not cc.is_workday(date_obj):  # 非工作日但没有具体节假日名称，即普通周末
                    return 2  # 普通周末
                else:
                    return 1  # 工作日

            temp_df['day_type'] = temp_df['date'].apply(get_day_type)

        except ImportError:
            print("⚠️ chinese_calendar库未安装，使用简化节假日逻辑")
            # 降级到原有的简化逻辑
            temp_df = preprocessor._with_holiday_features(temp_df, start_date, end_date)
            temp_df['day_type'] = 1  # 默认为工作日

        # 标识周末和节假日区域
        current_date = None
        current_holiday_status = 0
        start_time = None

        for i, (_, row) in enumerate(temp_df.iterrows()):
            date_str = row['date']
            day_type = row.get('day_type', 1)  # 日期类型：1=工作日, 2=周末, 3=法定节假日
            datetime_val = row['dateTime']

            # 判断是否为新的一天
            if current_date != date_str:
                # 如果前一天是节假日，结束前一天的区域
                if current_date is not None and current_holiday_status > 0:
                    prev_idx = max(0, i-1)
                    end_time = temp_df.iloc[prev_idx]['dateTime'] + pd.Timedelta(minutes=15)
                    self._fill_day_background(start_time, end_time, current_holiday_status)

                # 开始新的一天
                current_date = date_str
                # 根据day_type确定节假日状态：
                # day_type: 1=工作日, 2=周末, 3=法定节假日
                # current_holiday_status: 0=工作日, 1=法定节假日, 2=普通周末
                if day_type == 3:  # 法定节假日
                    current_holiday_status = 1  # 法定节假日（蓝色）
                elif day_type == 2:  # 普通周末
                    current_holiday_status = 2  # 普通周末（红色）
                else:  # 工作日
                    current_holiday_status = 0  # 工作日（白色）

                if current_holiday_status > 0:  # 任何类型的节假日
                    start_time = datetime_val

        # 处理最后一天
        if current_holiday_status > 0:
            end_time = temp_df.iloc[-1]['dateTime'] + pd.Timedelta(minutes=15)
            self._fill_day_background(start_time, end_time, current_holiday_status)



    def _fill_day_background(self, start_time, end_time, holiday_status):
        """
        填充单日的背景色

        :param start_time: 开始时间
        :param end_time: 结束时间
        :param holiday_status: 节假日状态 (1=法定节假日, 2=普通周末, 0=工作日)
        """
        if holiday_status == 1:  # 法定节假日
            color = 'lightblue'
            alpha = 0.4
        elif holiday_status == 2:  # 普通周末
            color = 'lightcoral'
            alpha = 0.3
        else:
            return  # 工作日不填充

        # 使用axvspan填充垂直区域
        plt.axvspan(start_time, end_time, color=color, alpha=alpha, zorder=2)