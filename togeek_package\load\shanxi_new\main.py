import pandas as pd
from time_series_plot import TimeSeriesPlotter

# 导入自定义模块
from lstm_model import LSTMModel  # LSTM模型
# from data_loader import aggregate_all_users_load  # 汇总所有用户负荷函数

# ======================== 在这里集中定义客户信息等配置 ========================

start_date = "2025-05-01"
end_date = "2025-07-17"
end_date_for_predict = "2025-07-12"
pred_date = "2025-07-16"

def main():
    """主函数 - 汇总所有用户负荷并进行LSTM预测"""
    global start_date, end_date

    print("🚀 山西零售用户负荷汇总与预测系统")
    print("=" * 60)

    # 1. 汇总所有用户的负荷数据
    # 注释掉原有的逐个sheet导入方式，改为直接导入已保存的总负荷数据
    # try:
    #     total_load_df, stats = aggregate_all_users_load(
    #         start_date = start_date,
    #         end_date = end_date,
    #         excel_file_path = "山西零售用户负荷.xlsx"
    #     )
    #
    #     print("\n📊 汇总统计信息:")
    #     print(f"   总用户数: {stats['total_users']}")
    #     print(f"   成功处理: {stats['successful_users']} 个用户")
    #     print(f"   时间点数: {stats['time_points']}")
    #     print(f"   负荷范围: {stats['total_load_min']:.2f} - {stats['total_load_max']:.2f} kW")
    #     print(f"   平均负荷: {stats['total_load_mean']:.2f} kW")
    #
    #     print(f"\n🎉 汇总完成！")
    #
    # except Exception as e:
    #     print(f"❌ 汇总失败: {str(e)}")
    #     return

    # 直接导入已保存的总负荷数据
    try:
        import glob
        # 查找最新的总负荷文件
        load_files = glob.glob("全部用户负荷总和_*.csv")
        if load_files:
            latest_file = max(load_files)  # 获取最新文件
            total_load_df = pd.read_csv(latest_file)
            total_load_df['datetime'] = pd.to_datetime(total_load_df['dateTime'])  # 注意列名是dateTime
            print(f"📁 直接导入总负荷数据: {latest_file}")
            print(f"📊 数据信息: {len(total_load_df)} 条记录")
            print(f"   负荷范围: {total_load_df['total_load'].min():.2f} - {total_load_df['total_load'].max():.2f} kW")
            print(f"   时间范围: {total_load_df['datetime'].min()} 到 {total_load_df['datetime'].max()}")
        else:
            print("❌ 未找到总负荷数据文件，请先运行完整的数据汇总")
            return
    except Exception as e:
        print(f"❌ 导入总负荷数据失败: {str(e)}")
        return

    # 2. 使用总负荷数据进行LSTM预测
    print(f"\n{'='*60}")
    print(f"开始总负荷LSTM预测")
    print(f"{'='*60}")

    try:
        # 准备数据
        config = LSTMModel.load_config()
        X_df, y_series, dates, preprocessor = LSTMModel.prepare_total_load_data(
            start_date, end_date, total_load_df, config
        )

        # 转换日期为datetime
        end_date_for_predict_dt = pd.to_datetime(end_date_for_predict)
        pred_date_dt = pd.to_datetime(pred_date)

        # 找到训练集结束时间点 (end_date_for_predict 23:45)
        train_end_time = end_date_for_predict_dt + pd.Timedelta(hours=23, minutes=45)

        # 找到pred_date这一天的开始和结束时间
        pred_day_start = pred_date_dt
        pred_day_end = pred_date_dt + pd.Timedelta(hours=23, minutes=45)

        # 创建完整的数据框
        merged_df = pd.DataFrame({'dateTime': dates, 'load': y_series})
        for col in X_df.columns:
            merged_df[col] = X_df[col]

        # 按时间划分
        train_mask = merged_df['dateTime'] <= train_end_time
        test_mask = (merged_df['dateTime'] > train_end_time) & (merged_df['dateTime'] <= pred_day_end)
        pred_day_mask = (merged_df['dateTime'] >= pred_day_start) & (merged_df['dateTime'] <= pred_day_end)

        # 划分训练集
        train_data = merged_df[train_mask]
        X_train = train_data.drop(columns=['load', 'dateTime'])
        y_train = train_data['load']
        dates_train = train_data['dateTime'].tolist()

        # 划分测试集 (从训练集结束后到pred_date结束)
        test_data = merged_df[test_mask]
        X_test = test_data.drop(columns=['load', 'dateTime'])
        y_test = test_data['load']
        dates_test = test_data['dateTime'].tolist()

        # 提取pred_date这一天的数据用于最终评估
        pred_day_data = merged_df[pred_day_mask]
        X_pred_day = pred_day_data.drop(columns=['load', 'dateTime'])
        y_pred_day = pred_day_data['load']
        dates_pred_day = pred_day_data['dateTime'].tolist()

        print(f"\n📊 数据划分结果:")
        print(f"   训练集: {len(X_train)} 样本")
        print(f"   测试集: {len(X_test)} 样本")
        print(f"   目标评估日: {len(X_pred_day)} 样本")

        print(f"\n⏰ 时间范围详情:")
        print(f"   📈 训练集时间范围:")
        print(f"      起始时间: {dates_train[0]}")
        print(f"      结束时间: {dates_train[-1]}")
        print(f"      时间跨度: {pd.to_datetime(dates_train[-1]) - pd.to_datetime(dates_train[0])}")

        print(f"   🔮 测试集时间范围:")
        print(f"      起始时间: {dates_test[0]}")
        print(f"      结束时间: {dates_test[-1]}")
        print(f"      时间跨度: {pd.to_datetime(dates_test[-1]) - pd.to_datetime(dates_test[0])}")

        print(f"   🎯 目标评估日时间范围:")
        print(f"      起始时间: {dates_pred_day[0]}")
        print(f"      结束时间: {dates_pred_day[-1]}")
        print(f"      时间跨度: {pd.to_datetime(dates_pred_day[-1]) - pd.to_datetime(dates_pred_day[0])}")

        if len(X_pred_day) != 96:
            print(f"⚠️ 警告: 目标评估日样本数为 {len(X_pred_day)}，预期为96个")
        else:
            print(f"✅ 目标评估日包含完整的96个时间点")

        # 训练LSTM模型
        lstm_model = LSTMModel.create_and_train(X_train, y_train, config)

        print(f"\n🔮 开始真实场景预测 (只使用 {end_date_for_predict} 23:45 之前的真实数据)")
        print(f"   从 {end_date_for_predict} 23:45 之后，所有负荷相关特征都使用预测值")

        # 创建一个虚拟的y_test_zeros，因为我们不想在预测过程中使用真实负荷值
        y_test_zeros = y_test * 0  # 全部设为0，表示没有真实负荷数据

        # 预测（不传入真实负荷数据，模拟真实预测场景）
        test_predictions = lstm_model.predict_multistep_real_scenario(X_train, X_test, y_test_zeros, end_date_for_predict_dt, y_train)['value']
        train_predictions = lstm_model.predict(X_train, y_train, dates_train)['value']

        # 从测试集预测结果中提取pred_date这一天的预测值
        # 找到pred_date在测试集中的索引范围
        test_dates_series = pd.Series(dates_test)
        pred_day_start_idx = None
        pred_day_end_idx = None

        for i, date in enumerate(test_dates_series):
            if pd.to_datetime(date).date() == pred_date_dt.date():
                if pred_day_start_idx is None:
                    pred_day_start_idx = i
                pred_day_end_idx = i

        if pred_day_start_idx is not None and pred_day_end_idx is not None:
            # 提取pred_date这一天的预测结果
            pred_day_predictions = test_predictions[pred_day_start_idx:pred_day_end_idx+1]

            print(f"\n🎯 {pred_date} 预测结果提取:")
            print(f"   在测试集中的索引范围: {pred_day_start_idx} 到 {pred_day_end_idx}")
            print(f"   提取的预测值数量: {len(pred_day_predictions)}")

            # 计算pred_date这一天的准确率
            accuracy = lstm_model.calculate_accuracy(y_pred_day, pred_day_predictions)
            print(f"\n🏆 {pred_date} 当天预测准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")

            # 保存pred_date这一天的详细结果
            pred_day_results_df = pd.DataFrame({
                'datetime': dates_pred_day,
                'actual_total_load': y_pred_day.reset_index(drop=True),
                'predicted_total_load': pred_day_predictions.reset_index(drop=True)
            })
            pred_day_results_df.to_csv(f"总负荷_LSTM_{pred_date}_预测结果.csv", index=False, encoding='utf-8-sig')
            print(f"💾 {pred_date} 详细预测结果已保存至: 总负荷_LSTM_{pred_date}_预测结果.csv")
        else:
            print(f"❌ 错误: 在测试集中未找到 {pred_date} 的数据")
            accuracy = 0.0

        # 获取真实的测试集负荷数据用于画图和计算准确率
        # 重新从原始数据中提取真实的y_test
        test_data_real = merged_df[test_mask]
        y_test_real = test_data_real['load']  # 真实的测试集负荷数据

        print(f"\n📊 使用真实负荷数据进行评估和画图:")
        print(f"   真实y_test范围: {y_test_real.min():.2f} - {y_test_real.max():.2f} kW")
        print(f"   真实y_test均值: {y_test_real.mean():.2f} kW")

        # 保存完整测试集结果和生成图表（使用真实负荷数据）
        test_results_df = pd.DataFrame({
            'datetime': dates_test,
            'actual_total_load': y_test_real.reset_index(drop=True),
            'predicted_total_load': test_predictions.reset_index(drop=True)
        })
        test_results_df.to_csv("总负荷_LSTM_预测结果.csv", index=False, encoding='utf-8-sig')

        plotter = TimeSeriesPlotter()
        plotter.plot_all_charts_and_calculate_metrics(
            dates_train, y_train, train_predictions,
            dates_test, y_test_real, test_predictions,  # 使用真实的y_test_real
            "总负荷_LSTM"
        )

        # 显示完整测试集的准确率（使用真实负荷数据计算）
        full_test_accuracy = lstm_model.calculate_accuracy(y_test_real, test_predictions)
        print(f"\n📊 完整测试集预测准确率: {full_test_accuracy:.4f} ({full_test_accuracy*100:.2f}%)")
        print(f"🎯 目标日期 {pred_date} 预测准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")

    except Exception as e:
        print(f"❌ 预测失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()